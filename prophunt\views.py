import json
import logging
from rest_framework import permissions
from rest_framework.decorators import api_view, permission_classes, renderer_classes
from rest_framework_swagger import renderers
from django.http.response import JsonResponse
from django.utils import timezone
from django.db import transaction

from account.models import Member
from game.models import <PERSON>ur<PERSON><PERSON>ithCoin, GameServer
from .models import (
    PropHuntGame, PropHuntParticipant, PropHuntSeason,
    BattlePassTier, BattlePassReward, BattlePassProgress, ClaimedReward,
    ProphauntWeapon, ProphauntWeaponOwns, ProphauntItemData
)
from .serializers import (
    PropHuntGameSerializer, PropHuntParticipantSerializer,
    BattlePassProgressSerializer, BattlePassRewardSerializer,
    ProphauntWeaponSerializer, ProphauntWeaponOwnsSerializer, ProphauntItemDataSerializer
)

logger = logging.getLogger('django')


@api_view(['POST'])
@permission_classes([])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def start_prophunt_game(request):
    """Start a new PropHunt game"""
    data = request.data

    server_id = data.get("server_id")
    if not server_id:
        return JsonResponse(status=400, data={"message": "server_id is required"})

    players = data.get("players", [])
    if len(players) < 2:
        return JsonResponse(status=400, data={"message": "At least 2 players required"})

    try:
        with transaction.atomic():
            # Create the game
            game = PropHuntGame.objects.create(
                server_id=server_id,
                max_players=data.get("max_players", 8),
                round_duration=data.get("round_duration", 300),
                status='waiting'
            )

            # Add participants
            for player_data in players:
                member_id = player_data.get("member_id")
                team = player_data.get("team")

                if not member_id or not team:
                    continue

                try:
                    member = Member.objects.get(id=member_id)
                    PropHuntParticipant.objects.create(
                        game=game,
                        member=member,
                        team=team
                    )
                except Member.DoesNotExist:
                    logger.warning(f"Member {member_id} not found for PropHunt game")
                    continue

            # Start the game
            game.start_game()

            return JsonResponse(status=200, data={
                "message": "PropHunt game started successfully",
                "game_id": game.id,
                "game": PropHuntGameSerializer(game).data
            })

    except Exception as e:
        logger.error(f"Error starting PropHunt game: {str(e)}")
        return JsonResponse(status=500, data={"message": "Failed to start game"})


@api_view(['POST'])
@permission_classes([])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def end_prophunt_game(request):
    """End a PropHunt game and calculate rewards"""
    data = request.data

    game_id = data.get("game_id")
    if not game_id:
        return JsonResponse(status=400, data={"message": "game_id is required"})

    try:
        game = PropHuntGame.objects.get(id=game_id, status='active')
    except PropHuntGame.DoesNotExist:
        return JsonResponse(status=404, data={"message": "Active game not found"})

    winning_team = data.get("winning_team")
    player_stats = data.get("player_stats", [])

    try:
        with transaction.atomic():
            # Update participant statistics
            for stat in player_stats:
                member_id = stat.get("member_id")
                kills = stat.get("kills", 0)
                deaths = stat.get("deaths", 0)
                survived = stat.get("survived", False)

                try:
                    participant = PropHuntParticipant.objects.get(
                        game=game,
                        member_id=member_id
                    )
                    participant.kills = kills
                    participant.deaths = deaths
                    participant.survived = survived
                    participant.save()
                except PropHuntParticipant.DoesNotExist:
                    logger.warning(f"Participant {member_id} not found in game {game_id}")
                    continue

            # End the game (this will calculate and distribute rewards)
            game.end_game(winning_team=winning_team)

            # Update battle pass progress for all participants
            current_season = PropHuntSeason.get_current_season()
            if current_season:
                for participant in game.participants.all():
                    progress, created = BattlePassProgress.objects.get_or_create(
                        member=participant.member,
                        season=current_season
                    )
                    progress.update_tier()

            return JsonResponse(status=200, data={
                "message": "PropHunt game ended successfully",
                "game": PropHuntGameSerializer(game).data,
                "participants": PropHuntParticipantSerializer(game.participants.all(), many=True).data
            })

    except Exception as e:
        logger.error(f"Error ending PropHunt game: {str(e)}")
        return JsonResponse(status=500, data={"message": "Failed to end game"})


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def get_battlepass_progress(request):
    """Get current battle pass progress for the authenticated user"""
    member = request.user

    current_season = PropHuntSeason.get_current_season()
    if not current_season:
        return JsonResponse(status=404, data={"message": "No active season found"})

    # Get or create progress
    progress, created = BattlePassProgress.objects.get_or_create(
        member=member,
        season=current_season
    )

    # Update tier based on current season smart
    progress.update_tier()

    # Get available rewards for current tier
    available_tiers = BattlePassTier.objects.filter(
        season=current_season,
        tier_number__lte=progress.current_tier
    ).prefetch_related('rewards')

    # Get claimed rewards
    claimed_reward_ids = ClaimedReward.objects.filter(
        member=member,
        reward__tier__season=current_season
    ).values_list('reward_id', flat=True)

    tier_data = []
    for tier in available_tiers:
        tier_rewards = []
        for reward in tier.rewards.all():
            reward_data = BattlePassRewardSerializer(reward).data
            reward_data['claimed'] = reward.id in claimed_reward_ids
            reward_data['can_claim'] = (
                not reward_data['claimed'] and
                (reward.tier_type == 'free' or member.season_premium)
            )
            tier_rewards.append(reward_data)

        tier_data.append({
            'tier_number': tier.tier_number,
            'smart_required': tier.smart_required,
            'rewards': tier_rewards
        })

    return JsonResponse(status=200, data={
        "season": {
            "name": current_season.name,
            "description": current_season.description,
            "max_tier": current_season.max_tier,
            "smart_per_tier": current_season.smart_per_tier
        },
        "progress": BattlePassProgressSerializer(progress).data,
        "member_season_smart": member.season_smart,
        "member_season_premium": member.season_premium,
        "tiers": tier_data
    })


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def claim_battlepass_reward(request):
    """Claim a battle pass reward"""
    data = request.data
    member = request.user

    reward_id = data.get("reward_id")
    if not reward_id:
        return JsonResponse(status=400, data={"message": "reward_id is required"})

    try:
        reward = BattlePassReward.objects.get(id=reward_id)
    except BattlePassReward.DoesNotExist:
        return JsonResponse(status=404, data={"message": "Reward not found"})

    # Check if reward is already claimed
    if ClaimedReward.objects.filter(member=member, reward=reward).exists():
        return JsonResponse(status=400, data={"message": "Reward already claimed"})

    # Get current season progress
    current_season = PropHuntSeason.get_current_season()
    if not current_season or reward.tier.season != current_season:
        return JsonResponse(status=400, data={"message": "Reward not available in current season"})

    progress, created = BattlePassProgress.objects.get_or_create(
        member=member,
        season=current_season
    )
    progress.update_tier()

    # Check if player has reached the required tier
    if progress.current_tier < reward.tier.tier_number:
        return JsonResponse(status=400, data={"message": "Tier not reached yet"})

    # Check if player has premium access for premium rewards
    if reward.tier_type == 'premium' and not member.season_premium:
        return JsonResponse(status=400, data={"message": "Premium battle pass required"})

    try:
        with transaction.atomic():
            # Claim the reward
            ClaimedReward.objects.create(member=member, reward=reward)

            # Apply reward to member
            if reward.reward_type == 'coin' and reward.coin_amount > 0:
                member.coin += reward.coin_amount
                member.save()

            # Log the reward claim
            member.log_data = member.log_data + f"battlepass_reward_claimed: {reward.name} (Tier {reward.tier.tier_number})\n"
            member.save()

            return JsonResponse(status=200, data={
                "message": "Reward claimed successfully",
                "reward": BattlePassRewardSerializer(reward).data
            })

    except Exception as e:
        logger.error(f"Error claiming reward: {str(e)}")
        return JsonResponse(status=500, data={"message": "Failed to claim reward"})


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def list_prophaunt_weapons(request):
    """List all PropHunt weapons and show which ones the member owns"""
    member = request.user

    # Get member's owned weapons
    owns = ProphauntWeaponOwns.objects.filter(member=member)
    owned_weapons = []
    for own in owns:
        owned_weapons.append(own)

    # Get all available weapons based on member's test status and game version
    if member.is_test:
        weapons = ProphauntWeapon.objects.filter(
            min_version__lte=member.game_version
        ).order_by("order")
    else:
        weapons = ProphauntWeapon.objects.filter(
            is_test=False,
            min_version__lte=member.game_version
        ).order_by("order")

    return JsonResponse(status=200, data={
        "my_weapons": ProphauntWeaponOwnsSerializer(owned_weapons, many=True).data,
        "weapons": ProphauntWeaponSerializer(weapons, many=True).data,
    })


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def get_prophaunt_item_data_client(request):
    """Get PropHunt item data for client"""
    member = request.user

    # Get weapon with id=1
    try:
        weapon_id_1 = ProphauntWeapon.objects.get(id=1)
    except ProphauntWeapon.DoesNotExist:
        return JsonResponse(status=404, data={"message": "Weapon with id=1 not found"})

    # Get or create ProphauntWeaponOwn for member and weapon with id=1
    weapon_own, weapon_own_created = ProphauntWeaponOwns.objects.get_or_create(
        member=member,
        weapon=weapon_id_1,
        defaults={
            'ammo': 100
        }
    )

    # Get or create ProphauntItemData
    item_data, created = ProphauntItemData.objects.get_or_create(
        member=member,
        defaults={
            'grenades': 3,
            'hexes': 5,
            'current_selected_weapon': weapon_id_1
        }
    )

    return JsonResponse(status=200, data={
        "item_data": ProphauntItemDataSerializer(item_data).data
    })


@api_view(['POST'])
@permission_classes([])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def get_prophaunt_item_data_server(request):
    """Get PropHunt item data for server"""
    data = request.data
    
    member:Member = request.user
    if not member.server_verified:
        return JsonResponse(status=400, data={"message": "Wrong Password"})

    server_key = data.get("server_key", "")
    gss = GameServer.objects.filter(server_key=server_key)
    if len(gss) == 0:
        return JsonResponse(status=400, data={
                    "message": "GTFO1"
                })
    gs:GameServer = gss[0]
    if not gs.trusted:
        return JsonResponse(status=400, data={
                    "message": "GTFO2"
                })


    members = Member.objects.filter(id=data.get("backend_id", 0))
    if len(members) == 0:
        return JsonResponse(status=401, data={
                    "message": "Member not found"
                })
    member:Member = members[0]

    # Get or create ProphauntItemData
    item_data, created = ProphauntItemData.objects.get_or_create(
        member=member,
        defaults={
            'grenades': 3,
            'hexes': 5,
            'current_selected_weapon': None
        }
    )

    return JsonResponse(status=200, data={
        "item_data": ProphauntItemDataSerializer(item_data).data,
        "player_id": data.get("player_id", "")
    })


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def purchase_prophaunt_weapon(request):
    """Purchase a PropHunt weapon with coins"""
    data = request.data
    member = request.user

    weapon_id = data.get("weapon_id")
    if not weapon_id:
        return JsonResponse(status=400, data={"message": "weapon_id is required"})

    try:
        weapon = ProphauntWeapon.objects.get(id=weapon_id)
    except ProphauntWeapon.DoesNotExist:
        return JsonResponse(status=404, data={"message": "Weapon not found"})

    # Check if weapon is available for this member
    if not member.is_test and weapon.is_test:
        return JsonResponse(status=400, data={"message": "Weapon not available"})

    if weapon.min_version > member.game_version:
        return JsonResponse(status=400, data={"message": "Weapon not found"})

    # Check if member already owns this weapon
    if ProphauntWeaponOwns.objects.filter(member=member, weapon=weapon).exists():
        return JsonResponse(status=400, data={"message": "Weapon already owned"})

    # Check if member has enough coins
    if member.coin < weapon.price:
        return JsonResponse(status=400, data={"message": "Insufficient coins"})

    try:
        with transaction.atomic():
            # Deduct coins
            member.coin -= weapon.price
            member.save()

            # Create weapon ownership
            weapon_own = ProphauntWeaponOwns.objects.create(
                member=member,
                weapon=weapon,
                ammo=100  # Default ammo
            )

            # Log the purchase
            PurchaseWithCoin.objects.create(
                member=member,
                type=f"PropHunt Weapon: {weapon.name}",
                price=weapon.price
            )

            return JsonResponse(status=200, data={
                "message": "Weapon purchased successfully",
                "weapon_own": ProphauntWeaponOwnsSerializer(weapon_own).data,
                "remaining_coins": member.coin
            })

    except Exception as e:
        logger.error(f"Error purchasing weapon: {str(e)}")
        return JsonResponse(status=500, data={"message": "Failed to purchase weapon"})


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def update_selected_weapon(request):
    """Update the player's currently selected weapon"""
    data = request.data
    member = request.user

    weapon_id = data.get("weapon_id")
    if not weapon_id:
        return JsonResponse(status=400, data={"message": "weapon_id is required"})

    try:
        weapon = ProphauntWeapon.objects.get(id=weapon_id)
    except ProphauntWeapon.DoesNotExist:
        return JsonResponse(status=404, data={"message": "Weapon not found"})

    # Check if weapon is available for this member
    if not member.is_test and weapon.is_test:
        return JsonResponse(status=400, data={"message": "Weapon not available"})

    if weapon.min_version > member.game_version:
        return JsonResponse(status=400, data={"message": "Weapon not available for your game version"})

    # Check if member owns this weapon
    if not ProphauntWeaponOwns.objects.filter(member=member, weapon=weapon).exists():
        return JsonResponse(status=400, data={"message": "You don't own this weapon"})

    try:
        with transaction.atomic():
            # Get or create ProphauntItemData for the member
            item_data, _ = ProphauntItemData.objects.get_or_create(
                member=member,
                defaults={
                    'grenades': 3,
                    'hexes': 5,
                    'current_selected_weapon': weapon
                }
            )

            # Update the selected weapon
            item_data.current_selected_weapon = weapon
            item_data.save()

            return JsonResponse(status=200, data={
                "message": "Selected weapon updated successfully",
                "item_data": ProphauntItemDataSerializer(item_data).data
            })

    except Exception as e:
        logger.error(f"Error updating selected weapon: {str(e)}")
        return JsonResponse(status=500, data={"message": "Failed to update selected weapon"})


@api_view(['POST'])
@permission_classes([])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def update_prophaunt_item_data_server(request):
    """Update PropHunt item data for server"""
    data = request.data
    
    member:Member = request.user
    if not member.server_verified:
        return JsonResponse(status=400, data={"message": "Wrong Password"})

    server_key = data.get("server_key", "")
    gss = GameServer.objects.filter(server_key=server_key)
    if len(gss) == 0:
        return JsonResponse(status=400, data={
                    "message": "GTFO1"
                })
    gs:GameServer = gss[0]
    if not gs.trusted:
        return JsonResponse(status=400, data={
                    "message": "GTFO2"
                })


    members = Member.objects.filter(id=data.get("backend_id", 0))
    if len(members) == 0:
        return JsonResponse(status=401, data={
                    "message": "Member not found"
                })
    member:Member = members[0]

    # Get or create ProphauntItemData
    item_data, _ = ProphauntItemData.objects.get_or_create(member=member)
    item_data.grenades = data.get("grenades", 0)
    item_data.hexes = data.get("hexes", 0)
    item_data.save()


    return JsonResponse(status=200, data={
        "message": "Item data updated successfully"
    })