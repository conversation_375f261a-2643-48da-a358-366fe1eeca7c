from django.db import models
from django.utils import timezone
from utils.models import BaseModel


class DailyActiveUsers(BaseModel):
    """
    Model to track daily active users statistics.
    This model stores the count of unique active users for each day.
    """
    
    date = models.DateField(
        unique=True,
        help_text="The date for which the active users are counted"
    )
    
    total_active_users = models.IntegerField(
        default=0,
        help_text="Total number of unique users who were active on this date"
    )
    
    new_users = models.IntegerField(
        default=0,
        help_text="Number of new users who registered on this date"
    )
    
    returning_users = models.IntegerField(
        default=0,
        help_text="Number of users who were active on this date but registered before"
    )
    
    # Game-specific activity metrics
    users_played_games = models.IntegerField(
        default=0,
        help_text="Number of users who played at least one game on this date"
    )
    
    users_made_purchases = models.IntegerField(
        default=0,
        help_text="Number of users who made at least one purchase on this date"
    )
    
    # Market breakdown
    myket_users = models.IntegerField(
        default=0,
        help_text="Number of active users from Myket market"
    )
    
    googleplay_users = models.IntegerField(
        default=0,
        help_text="Number of active users from Google Play market"
    )
    
    bazaar_users = models.IntegerField(
        default=0,
        help_text="Number of active users from Bazaar market"
    )
    
    desktop_users = models.IntegerField(
        default=0,
        help_text="Number of active users from Desktop"
    )
    
    other_market_users = models.IntegerField(
        default=0,
        help_text="Number of active users from other markets"
    )
    
    class Meta:
        ordering = ['-date']
        verbose_name = "Daily Active Users"
        verbose_name_plural = "Daily Active Users"
    
    def __str__(self):
        return f"DAU for {self.date}: {self.total_active_users} users"
    
    @property
    def retention_rate(self):
        """Calculate the retention rate (returning users / total users)"""
        if self.total_active_users == 0:
            return 0
        return round((self.returning_users / self.total_active_users) * 100, 2)
    
    @property
    def new_user_rate(self):
        """Calculate the new user rate (new users / total users)"""
        if self.total_active_users == 0:
            return 0
        return round((self.new_users / self.total_active_users) * 100, 2)
    
    @property
    def gaming_engagement_rate(self):
        """Calculate the percentage of users who played games"""
        if self.total_active_users == 0:
            return 0
        return round((self.users_played_games / self.total_active_users) * 100, 2)
    
    @property
    def purchase_conversion_rate(self):
        """Calculate the percentage of users who made purchases"""
        if self.total_active_users == 0:
            return 0
        return round((self.users_made_purchases / self.total_active_users) * 100, 2)
