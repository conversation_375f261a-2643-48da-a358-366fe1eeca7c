import json

from django.contrib.admin.models import Log<PERSON>ntry, DELETION
from django.utils.html import escape
#from django.core.urlresolvers import reverse
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as DjangoUserAdmin
from django.utils.translation import ugettext_lazy as _
from datetime import timedelta
from game.models import JobDone, CoinTransfer
from .models.member import Member, MemberData, ContentCreator
from .models.sync_data import SyncData, SiteElement, SecretKey
from .models.meta_progress import MetaProgressLevel
from .models.daily_active_users import DailyActiveUsers
from utils.admin import BaseUserAdmin, BaseAdmin
from utils.utils import send_email


def fraud_kill(modeladmin, request, queryset):
    for member in queryset:
        member.fraud_kill(delete_characters_time=timedelta(days=1))
fraud_kill.short_description = "Fraud Kill Member(Coin = 0) Remove last 24h unlocked characters."

def fraud_kill_all(modeladmin, request, queryset):
    for member in queryset:
        member.fraud_kill(-1)
fraud_kill_all.short_description = "Reset The account (coin = 0, remove all characters)"


def fire_job(modeladmin, request, queryset):
    for member in queryset:
        member.title = ""
        member.police_job_enabled = False
        member.save()
        member_data = member.data.all()[0]
        data = member_data.data
        data["arm"] = None
        if "job_stats" in data:
            for key in data["job_stats"]:
                data["job_stats"][key] = 0
        member_data.data_string = json.dumps(data)
        member_data.save()
fire_job.short_description = "Fire From Job"


def clear_job_done(member, job):
    JobDone.objects.filter(member=member, job=job).delete()


def clear_police_job_done(modeladmin, request, queryset):
    for member in queryset:
        clear_job_done(member=member, job="policeReport")
clear_police_job_done.short_description = "Clear policeReport JobDones"


def add_coins(queryset, coins):
    for member in queryset:
        member.coin += coins
        if member.coin < 0:
            member.coin = 0
        member.save()
def add_coin100(modeladmin, request, queryset):
    add_coins(queryset, coins=100)
add_coin100.short_description = "+100 Coins"
def add_coin500(modeladmin, request, queryset):
    add_coins(queryset, coins=500)
add_coin500.short_description = "+500 Coins"
def add_coin1000(modeladmin, request, queryset):
    add_coins(queryset, coins=1000)
add_coin1000.short_description = "+1000 Coins"
def add_coinm100(modeladmin, request, queryset):
    add_coins(queryset, coins=-100)
add_coinm100.short_description = "-100 Coins"
def add_coinm200(modeladmin, request, queryset):
    add_coins(queryset, coins=-200)
add_coinm200.short_description = "-200 Coins"
def add_coinm500(modeladmin, request, queryset):
    add_coins(queryset, coins=-500)
add_coinm500.short_description = "-500 Coins"
def add_coinm1000(modeladmin, request, queryset):
    add_coins(queryset, coins=-1000)
add_coinm1000.short_description = "-1000 Coins"
def add_coinm5000(modeladmin, request, queryset):
    add_coins(queryset, coins=-5000)
add_coinm5000.short_description = "-5000 Coins"
def add_coinm10000(modeladmin, request, queryset):
    add_coins(queryset, coins=-10000)
add_coinm10000.short_description = "-10000 Coins"
def add_coin1020(modeladmin, request, queryset):
    add_coins(queryset, coins=1020)
add_coin1020.short_description = "1020 Coins"
def add_coin2150(modeladmin, request, queryset):
    add_coins(queryset, coins=2150)
add_coin2150.short_description = "2150 Coins"
def add_coin4000(modeladmin, request, queryset):
    add_coins(queryset, coins=4000)
add_coin4000.short_description = "4000 Coins"
def add_coin10000(modeladmin, request, queryset):
    add_coins(queryset, coins=10000)
add_coin10000.short_description = "10000 Coins"
def add_coin15000(modeladmin, request, queryset):
    add_coins(queryset, coins=15000)
add_coin15000.short_description = "15000 Coins"
def add_coin30000(modeladmin, request, queryset):
    add_coins(queryset, coins=30000)
add_coin30000.short_description = "30000 Coins"
def add_coin5000(modeladmin, request, queryset):
    add_coins(queryset, coins=5000)
add_coin5000.short_description = "5000 Coins"
def add_coin20000(modeladmin, request, queryset):
    add_coins(queryset, coins=20000)
add_coin20000.short_description = "20k Coins"
def add_coin50000(modeladmin, request, queryset):
    add_coins(queryset, coins=50000)
add_coin50000.short_description = "50k Coins"


def full_ban(modeladmin, request, queryset):
    for member in queryset:
        member.full_ban = True
        member.coin = 0
        member.cup = 0
        member.crown = 0
        member.save()
        member.fraud_kill(-1)
        transfers = CoinTransfer.objects.filter(from_member=member, success=True)
        mail_body = ""
        transaction: CoinTransfer
        for transaction in transfers:
            member: Member = transaction.to_member
            if member:
                mail_body += member.username + ": " + str(transaction.amount) + ": before=" + str(member.coin)
                member.coin -= transaction.amount
                if member.coin < 0:
                    member.coin = 0
                mail_body += " after=" + str(member.coin) + "\n"
                member.save()
            else:
                mail_body += "member is None: transaction id: " + str(transaction.id)
            
        
        send_email.delay("<EMAIL>", "Revert transactions", mail_body)
full_ban.short_description = "Full Ban Selected Users"


def unban_users(modeladmin, request, queryset):
    for member in queryset:
        member.ban_till_time = None
        member.save()
unban_users.short_description = "Unban Selected Users"


def release_jail_users(modeladmin, request, queryset):
    for member in queryset:
        member.jail_till_time = None
        member.in_prison = False
        member.save()
release_jail_users.short_description = "Release Jail Selected Users"


def alive_users(modeladmin, request, queryset):
    for member in queryset:
        member_data = member.data.all()[0]
        json_data = member_data.data
        json_data["is_dead"] = False
        member_data.data_string = json.dumps(json_data)
        member_data.save()
alive_users.short_description = "Alive Selected Users"


def jail_users(modeladmin, request, queryset):
    for member in queryset:
        member.add_jail(30 * 60)
        member.in_prison = True
        member.save()
jail_users.short_description = "Jail Selected Users"


def ban_users_time(queryset, seconds):
    for member in queryset:
        member.add_ban(seconds, False)


def ban_users1h(modeladmin, request, queryset):
    ban_users_time(queryset, seconds=3600 * 1)
ban_users1h.short_description = "1h Ban Selected Users From Chat"


def ban_users4h(modeladmin, request, queryset):
    ban_users_time(queryset, seconds=3600 * 4)
ban_users4h.short_description = "4h Ban Selected Users From Chat"


def ban_users24h(modeladmin, request, queryset):
    ban_users_time(queryset, seconds=3600 * 24)
ban_users24h.short_description = "24h Ban Selected Users From Chat"


def ban_users7d(modeladmin, request, queryset):
    ban_users_time(queryset, seconds=7 * 3600 * 24)
ban_users7d.short_description = "7d Ban Selected Users From Chat"


def police_ban(modeladmin, request, queryset):
    ban_users_time(queryset, seconds=3600 * 24 * 7)
    for member in queryset:
        clear_job_done(member=member, job="policeReport")
    for member in queryset:
        member.title = ""
        member.police_job_enabled = True
        member.save()
        member_data = member.data.all()[0]
        data = member_data.data
        data["arm"] = None
        if "job_stats" in data:
            for key in data["job_stats"]:
                data["job_stats"][key] = 0
        member_data.data_string = json.dumps(data)
        member_data.save()
police_ban.short_description = "PoliceBan: 7d Ban User, zero the jobdones"


def doctor_ban(modeladmin, request, queryset):
    ban_users_time(queryset, seconds=3600 * 24)
    for member in queryset:
        clear_job_done(member=member, job="heal")
        clear_job_done(member=member, job="visit")
    for member in queryset:
        member.title = ""
        member.save()
        member_data = member.data.all()[0]
        data = member_data.data
        data["arm"] = None
        if "job_stats" in data:
            for key in data["job_stats"]:
                data["job_stats"][key] = 0
        member_data.data_string = json.dumps(data)
        member_data.save()
doctor_ban.short_description = "DoctorBan: 1d Ban User, zero the jobdones"


class MemberAdmin(BaseUserAdmin, DjangoUserAdmin):
    fieldsets = (
        (None, {'fields': ('username', 'password', 'title', 'linked_member', 'handle', 'progress_level', 'game_count', 'win_count', 'first_place_count', 'email', 'email_code', 'remove_code',
                            'crown', 'cup', 'coin', 'hunger', 'warmth', 'market', 'last_daily_claim', 'tutorial_finished', 'game_version', 'name_changed_count', 'device',
                                 'ban_till_time', 'is_test', 'chat_admin', 'free_admin', 'elite_bazras', 'bazras', 'stealth', 'show_debug', 'vehicle_spawn', 'desktop_verified',  'jail_till_time', 'in_prison', 'police_job_enabled', 'hide_in_ranks', 'log_data')}),

        (_('Permissions'), {'fields': ('is_active', 'is_staff', 'is_superuser',
                                       'groups', 'user_permissions')}),

        (_('Important Stuff'), {'fields': ('server_verified', 'last_login', 'presence', 'presence_data', 'presence_last_update', 'switch_to_zarinpal', 'referer_content_creator', 'last_suspicous_email', 'ipg_shop_discount', 'force_iap',
                                            'force_ipg', 'full_ban')}),
    )
    list_display = ('id', 'created', 'username', 'handle', 'title', 'coin', 'cup', 'crown', 'smart',
                    'police_job_enabled', 'game_version', 'full_ban')
    list_filter = ('title', 'police_job_enabled', 'hide_in_ranks', 'full_ban', 'market', 'created', 'bazras', 'switch_to_zarinpal', 'desktop_verified')
    search_fields = ('username', 'id', 'handle', 'email')
    ordering = ('-date_joined',)
    readonly_fields = ['linked_member']
    actions = [add_coin100, add_coin500, add_coin1000, add_coin1020, add_coin2150, add_coin4000, add_coin5000, add_coin10000,
                add_coin15000, add_coin20000, add_coin30000, add_coin50000,
                add_coinm100, add_coinm200, add_coinm500, add_coinm1000, add_coinm5000, add_coinm10000,
               ban_users1h, ban_users4h, ban_users24h, ban_users7d, unban_users, jail_users, release_jail_users,
                 fire_job, alive_users, clear_police_job_done, doctor_ban, police_ban, full_ban, fraud_kill_all]
    list_editable = ('police_job_enabled',)


class MemberDataAdmin(BaseAdmin):
    list_display = ('id', 'member')
    list_editable = ()
    list_filter = ('created',)
    readonly_fields = ('member',)
    search_fields = ('member__id', 'member__handle', 'member__username')


class SyncDataAdmin(BaseAdmin):
    list_display = ('id', 'game_is_down', 'up_datetime', "last_version", "force_version")
    list_editable = ()
    list_filter = ('created',)


class SyncDataAdmin(BaseAdmin):
    list_display = ('id', 'game_is_down', 'up_datetime', "last_version", "force_version")
    list_editable = ()
    list_filter = ('created',)


class MetaProgressAdmin(BaseAdmin):
    list_display = ('id', 'level', 'progress_type', 'game_count', "cup", "prize_type", 'prize_coin', 'prize_character', 'next')
    list_editable = ()
    list_filter = ('created',)


class LogEntryAdmin(admin.ModelAdmin):

    date_hierarchy = 'action_time'

    # readonly_fields = LogEntry._meta.get_all_field_names()

    readonly_fields = ('user',)


    list_filter = [
        #'user',
        'content_type',
        'action_flag'
    ]

    search_fields = [
        'object_repr',
        'change_message'
    ]


    list_display = [
        'action_time',
        'user',
        'content_type',
        'object_link',
        'action_flag',
        'change_message',
    ]

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return request.user.is_superuser and request.method != 'POST'

    def has_delete_permission(self, request, obj=None):
        return False

    def object_link(self, obj):
        return obj.__str__()
        # if obj.action_flag == DELETION:
        #     link = escape(obj.object_repr)
        # else:
        #     ct = obj.content_type
        #     link = u'<a href="%s">%s</a>' % (
        #         reverse('admin:%s_%s_change' % (ct.app_label, ct.model), args=[obj.object_id]),
        #         escape(obj.object_repr),
        #     )
        # return link
    object_link.allow_tags = True
    object_link.admin_order_field = 'object_repr'
    object_link.short_description = u'object'
    
    def queryset(self, request):
        return super(LogEntryAdmin, self).queryset(request) \
            .prefetch_related('content_type')


class SiteElementsAdmin(BaseAdmin):
    list_display = ('id', 'name', 'element', 'order', 'visible')
    list_editable = ('order', 'visible')
    list_filter = ('created', 'visible')


def cashout_test(modeladmin, request, queryset):
    c: ContentCreator
    for c in queryset:
        c.cashout_now(False)
cashout_test.short_description = "Cashout for testing"

def real_cashout(modeladmin, request, queryset):
    c: ContentCreator
    for c in queryset:
        c.cashout_now(True)
real_cashout.short_description = "Real Cashout"

class ContentCreatorAdmin(BaseAdmin):
    list_display = ('id', 'name', 'member', 'code', "number_of_followers", "purchase_count", "purchase_sum_", "coin_revenue", "is_coin", "enabled")
    list_editable = ('enabled',)
    raw_id_fields = ('member',)
    actions = [cashout_test, real_cashout] 
    list_filter = ('created', 'is_coin', 'enabled')


class SecretKeyAdmin(BaseAdmin):
    list_display = ('id', 'created', 'key')
    list_editable = ()
    list_filter = ()


class DailyActiveUsersAdmin(BaseAdmin):
    list_display = (
        'date', 'total_active_users', 'new_users', 'returning_users',
        'users_played_games', 'users_made_purchases', 'retention_rate',
        'gaming_engagement_rate', 'purchase_conversion_rate'
    )
    list_filter = ('date', 'created')
    search_fields = ('date',)
    readonly_fields = (
        'retention_rate', 'new_user_rate', 'gaming_engagement_rate',
        'purchase_conversion_rate'
    )
    ordering = ('-date',)

    fieldsets = (
        ('Date Information', {
            'fields': ('date',)
        }),
        ('User Activity Metrics', {
            'fields': (
                'total_active_users', 'new_users', 'returning_users',
                'users_played_games', 'users_made_purchases'
            )
        }),
        ('Market Breakdown', {
            'fields': (
                'myket_users', 'googleplay_users', 'bazaar_users',
                'desktop_users', 'zarinpal_users', 'other_market_users'
            ),
            'classes': ('collapse',)
        }),
        ('Calculated Metrics', {
            'fields': (
                'retention_rate', 'new_user_rate', 'gaming_engagement_rate',
                'purchase_conversion_rate'
            ),
            'classes': ('collapse',)
        })
    )


admin.site.register(SiteElement, SiteElementsAdmin)
admin.site.register(MetaProgressLevel, MetaProgressAdmin)
admin.site.register(SyncData, SyncDataAdmin)
admin.site.register(Member, MemberAdmin)
admin.site.register(MemberData, MemberDataAdmin)
admin.site.register(LogEntry, LogEntryAdmin)
admin.site.register(ContentCreator, ContentCreatorAdmin)
admin.site.register(SecretKey, SecretKeyAdmin)
admin.site.register(DailyActiveUsers, DailyActiveUsersAdmin)
