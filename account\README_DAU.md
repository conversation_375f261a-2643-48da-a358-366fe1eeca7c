# Daily Active Users (DAU) Model and Tasks

This module provides functionality to track and calculate daily active users statistics for the Animal Rush game.

## Model: DailyActiveUsers

The `DailyActiveUsers` model stores comprehensive daily statistics including:

### Core Metrics
- `total_active_users`: Total number of unique users who were active on the date
- `new_users`: Number of new users who registered on the date
- `returning_users`: Number of users who were active but registered before the date

### Activity Metrics
- `users_played_games`: Number of users who played at least one game
- `users_made_purchases`: Number of users who made at least one purchase

### Market Breakdown
- `myket_users`: Active users from Myket market
- `googleplay_users`: Active users from Google Play market
- `bazaar_users`: Active users from Bazaar market
- `desktop_users`: Active users from Desktop
- `zarinpal_users`: Active users from Zarinpal market
- `other_market_users`: Active users from other markets

### Calculated Properties
- `retention_rate`: Percentage of returning users vs total users
- `new_user_rate`: Percentage of new users vs total users
- `gaming_engagement_rate`: Percentage of users who played games
- `purchase_conversion_rate`: Percentage of users who made purchases

## Tasks

### calculate_daily_active_users(target_date=None)

Calculates and stores DAU statistics for a specific date.

**Parameters:**
- `target_date` (str, optional): Date in YYYY-MM-DD format. If None, uses yesterday.

**Returns:**
- dict: Summary of calculated statistics

**Example Usage:**
```python
from account.tasks import calculate_daily_active_users

# Calculate for yesterday (default)
result = calculate_daily_active_users()

# Calculate for a specific date
result = calculate_daily_active_users('2025-09-01')
```

### calculate_weekly_dau_batch()

Calculates DAU for the last 7 days in batch. Useful for backfilling data.

**Returns:**
- list: List of results for each day

### cleanup_old_dau_records(days_to_keep=365)

Cleans up old DAU records to prevent database bloat.

**Parameters:**
- `days_to_keep` (int): Number of days of records to keep (default: 365)

**Returns:**
- int: Number of records deleted

## Usage in Production

### Daily Calculation (Recommended)
Set up a daily cron job or Celery beat schedule to run:
```python
calculate_daily_active_users.delay()
```

### Celery Beat Configuration
The tasks have been added to the Celery beat schedule in `game/tasks.py`:
```python
app.conf.beat_schedule = {
    # ... other tasks ...
    'calculate_daily_active_users': {
        'task': 'account.tasks.calculate_daily_active_users',
        'schedule': 3600 * 24,  # Run daily (24 hours)
        'args': ()
    },
    'cleanup_old_dau_records': {
        'task': 'account.tasks.cleanup_old_dau_records',
        'schedule': 3600 * 24 * 30,  # Run monthly (30 days)
        'args': ()
    },
}
```

## Admin Interface

The DAU data can be viewed and managed through the Django admin interface at `/admin/account/dailyactiveusers/`.

The admin interface provides:
- List view with key metrics
- Filtering by date
- Readonly calculated fields
- Organized fieldsets for easy viewing

## Verification

To verify the setup is working correctly:

### Check Beat Schedule
```bash
python -c "import os; import django; os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'game_backend.settings'); django.setup(); from game.tasks import app; print('Beat schedule tasks:', list(app.conf.beat_schedule.keys()))"
```

### Manual Task Execution
```python
# In Django shell or script
from account.tasks import calculate_daily_active_users
result = calculate_daily_active_users('2025-09-01')  # Calculate for specific date
print(result)
```

### Check Celery Worker and Beat Status
```bash
# Start Celery worker (in production)
celery -A game_backend worker -c 50 -l info

# Start Celery beat scheduler (in production)
celery -A game_backend beat
```

## Data Definition

A user is considered "active" if they have:
- Updated their `last_login` field on the target date, OR
- Updated their `presence_last_update` field on the target date

Test users (where `is_test=True`) are excluded from all calculations.
